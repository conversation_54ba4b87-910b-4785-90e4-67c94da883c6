import { <PERSON>mpo<PERSON>, OnInit, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { Chart, PieController, ArcElement, Tooltip, Legend } from 'chart.js';
import { BonusMeasuresService } from './services/bonus-measures.service';
import { BonusMeasure } from 'src/app/shared-services/bonus-measures/model/bonus-measures-model';

// Apply jsPDF plugin
applyPlugin(jsPDF);

// Register Chart.js components
Chart.register(PieController, ArcElement, Tooltip, Legend);

@Component({
  selector: 'app-bonus-measures',
  templateUrl: './bonus-measures.component.html',
  styleUrl: './bonus-measures.component.scss'
})
export class BonusMeasuresComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  bonusMeasures: BonusMeasure[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  columnWidths: number[] = []; // External variable to store column widths
  groupByFields: number[] = []; // groupBy fields
  toggleGrouping: boolean = false; // Flag to track grouping state
  menuOpen = false;

  constructor(
    private bonusMeasuresService: BonusMeasuresService,
    private router: Router,
    private spinnerService: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this.loadBonusMeasures();
  }

  ngAfterViewInit(): void {
    // Initialize resize observer for responsive table
    this.initializeResizeObserver();
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.table) {
      this.table.destroy();
    }
  }

  /**
   * Load bonus measures data from the service
   */
  loadBonusMeasures(): void {
    this.spinnerService.show();

    const subscription = this.bonusMeasuresService.getBonusMeasures().subscribe({
      next: (data) => {
        this.bonusMeasures = data;
        this.spinnerService.hide();

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading bonus measures data:', error);
        this.spinnerService.hide();
      }
    });
  }

  /**
   * Initialize the resize observer for responsive table behavior
   */
  initializeResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.table) {
          this.table.redraw();
        }
      });

      const tableContainer = document.querySelector('#bonus-measures-table');
      if (tableContainer) {
        this.resizeObserver.observe(tableContainer);
      }
    }
  }

  /**
   * Toggle the export menu
   */
  toggleMenu(): void {
    this.menuOpen = !this.menuOpen;
  }

  /**
   * Handle clicks outside the menu to close it
   */
  onClickOutsideMenu(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown')) {
      this.menuOpen = false;
    }
  }

  /**
   * Initialize the Tabulator table with bonus measures data
   */
  initializeTable(): void {
    this.table = new Tabulator("#bonus-measures-table", {
      layoutColumnsOnNewData:true,
      pagination: true,
      height: "99%",
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false, //disable row groups in download
      },
      data: this.bonusMeasures,
      printAsHtml: true, //enable html table printing
      printStyled: true, //copy Tabulator styling to HTML table
      groupBy: ["measureNm", "locationGroupName", "location"],
      groupStartOpen: [true, true, true, true],
      groupToggleElement: "header", //toggle group on click anywhere in the group header
      headerSortElement: "<i class='material-icons'>arrow_drop_up</i>", //material icon for sorting
      rowHeight: 30,
      columnDefaults:{
        resizable: false,
        headerSort: false,
      },
      columnHeaderVertAlign: "middle",
      columns: [
        { title: "Measure", field: "measureNm", visible: false, download: true }, //these 3 columns are hidden in the table but included in the download
        { title: "Group Name", field: "locationGroupName", visible: false, download: true },
        { title: "Location Name", field: "location", visible: false, download: true },
        { //the dummy column
          download: false,
          field: "filter", //a dummy field to allow the .getField() method to work
          title: "<span style='padding-right: 140px;'>Measure</span>Location",
          hozAlign: "center",
          width: 400,
        },

        {
          title: "Provider",
          field: "primaryProvider",
          width: 200,
        },
        {
          title: "Qualifies",
          field: "totCohortCnt",
          hozAlign: "center",
          width: 100,
          cssClass: "whiteBorder"
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon satisfied-icon'></div></div>",
          titleDownload: "Measures Satisfied",
          field: "cohortCnt",
          hozAlign: "right",
          width: 120,
          cssClass: "darkBlue"
        },
        {
          title: "Measures Satisfied",
          titleDownload: "%",
          field: "satisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          width: 220,
          cssClass: "lightBlue",
          formatter: (cell: any) => `${cell.getValue()}%`
        },
        {
          title: "",
          field: "pieChart",
          download: false,
          formatter: (cell: any) => {
            const data = cell.getRow().getData();
            const canvas = document.createElement("canvas");
            canvas.width = 20;
            canvas.height = 20;

            requestAnimationFrame(() => {
              this.drawPieChart(
                canvas,
                data.cohortCnt,
                data.totCohortCnt - data.cohortCnt,
                "#99d2f8",
                "#0071bc"
              );
            });
            return canvas;
          },
          hozAlign: "center",
          minWidth: 20,
          maxWidth: 30,
        },
        {
          title: "<div style='text-align: right'><div class='svg-icon unsatisfied-icon'></div></div>",
          titleDownload: "Measures Unsatisfied",
          field: "unsatisfiedCount",
          hozAlign: "right",
          width: 100,
          cssClass: "darkBlue"
        },
        {
          title: "Measures Unsatisfied",
          titleDownload: "%",
          field: "unsatisfiedPercentage",
          hozAlign: "center",
          headerWordWrap: true,
          //width: 220,
          cssClass: "darkBlue",
          formatter: (cell: any) => `(${cell.getValue()}%)`
        }
      ]
    });

    // Subscribe to the tableBuilt event
    this.table.on("tableBuilt", () => {
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
      this.groupByFields = this.table.options.groupBy;

      // Set the group headers after the table is built
      this.table.setGroupHeader((value: any, _count: any, data: any[], group: any) => {
        // Calculate the summation of the columns
        const totalCohort = data.reduce((sum: number, row: any) => sum + row.totCohortCnt, 0);
        const measuresSatisfied = data.reduce((sum: number, row: any) => sum + row.cohortCnt, 0);
        const measuresUnsatisfied = data.reduce((sum: number, row: any) => sum + row.unsatisfiedCount, 0);
        const satisfiedPercentage = totalCohort > 0 ? Math.round((measuresSatisfied / totalCohort) * 100) : 0;
        const unsatisfiedPercentage = totalCohort > 0 ? Math.round((measuresUnsatisfied / totalCohort) * 100) : 0;

        // Determine which grouping level is being rendered
        const groupField = group.getField();
        const groupLevel = this.groupByFields.indexOf(groupField);
        const paddingLevel = 100; //padding on the left side of each column
        let indentationWidth: number = 32 + (groupLevel * paddingLevel);

                // Create the custom group header HTML string
        let groupHeaderHtml = `<div class="tabulator-group-toggle tabulator-row tabulator-unselectable tabulator-calcs tabulator-calcs-top tabulator-row-even" role="row">`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="display: none;">&nbsp;</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[3] + this.columnWidths[4] - indentationWidth}px;">${value}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[5]}px; text-align: center;">${totalCohort}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[6]}px; text-align: right;">${measuresSatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell lightBlue" role="gridcell" style="width: ${this.columnWidths[7]}px; text-align: center;">(${satisfiedPercentage}%)</div>`;
        groupHeaderHtml += `<div class="tabulator-cell" role="gridcell" style="width: ${this.columnWidths[8]}px; text-align: center;" data-piechart data-satisfied="${measuresSatisfied}" data-unsatisfied="${measuresUnsatisfied}"></div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[9]}px; text-align: right;">${measuresUnsatisfied}</div>`;
        groupHeaderHtml += `<div class="tabulator-cell darkBlue" role="gridcell" style="width: ${this.columnWidths[10]}px; text-align: center;">(${unsatisfiedPercentage}%)</div>`;
        groupHeaderHtml += `</div>`;

        return groupHeaderHtml;
      });

      // Attach scroll synchronization logic
      const tableElement = document.querySelector("#bonus-measures-table .tabulator-tableholder");
      if (tableElement) {
        tableElement.addEventListener("scroll", (event) => {
          const scrollLeft = (event.target as HTMLElement).scrollLeft;
          this.syncGroupHeaderScroll(scrollLeft);
        });
      }
    });

    this.table.on("columnResized", () => {
      // Update custom group column widths when columns are resized
      const columns = this.table.getColumns();
      this.columnWidths = columns.map((col: any) => col.getWidth());
    });

    this.table.on("rowClick", (_e: any, row: any) => {
      // Handle row click event
      // Create the URL to navigate to the Measures Detail Grid
      // /Dashboard/MeasuresDetail?year=2025&cohortId=1004&location=AA_NEWNAN HCC&provider=AHMED MOUSSA, MD&rollingWeek=27&alertLvl=0&measureCd=AHF016
      // would it be better to pass the entire row data as a query parameter?
      const rowData = row.getData();
      const url = `/Dashboard/MeasuresDetail?year=${rowData.reportingPeriod}&cohortId=${rowData.cohortId}&location=${rowData.location}&provider=${rowData.primaryProvider}&rollingWeek=${rowData.rollingWeek}&alertLvl=${rowData.invertedFlg ? 0 : 1}&measureCd=${rowData.measureCd}`;
      //this.router.navigateByUrl(url);
      console.log('Navigating to: ' + url);
    });

    this.table.on("renderComplete", () => {
      this.renderGroupHeaderPieCharts();
    });
  }

  /**
   * Synchronize group header scroll with table scroll
   */
  syncGroupHeaderScroll(scrollLeft: number): void {
    // Find all group headers and synchronize their scroll position
    // with the table's scroll position
    const groupHeaders = document.querySelectorAll(".tabulator-group");
    groupHeaders.forEach((header) => {
      (header as HTMLElement).style.transform = `translateX(-${scrollLeft}px)`;
    });
  }

  /**
   * Render pie charts in group headers
   */
  renderGroupHeaderPieCharts(): void {
    // Find all elements with the data-piechart attribute
    const pieChartElements = document.querySelectorAll('[data-piechart]');
    pieChartElements.forEach((element) => {
      // Clear any existing content
      element.innerHTML = '';

      const canvas = document.createElement('canvas');
      canvas.width = 20;
      canvas.height = 20;

      const satisfied = parseInt(element.getAttribute('data-satisfied') || '0', 10);
      const unsatisfied = parseInt(element.getAttribute('data-unsatisfied') || '0', 10);

      // Draw the pie chart using Chart.js
      this.drawPieChart(canvas, satisfied, unsatisfied, "#99d2f8", "#0071bc");

      // Append the canvas to the element
      element.appendChild(canvas);
    });
  }

  /**
   * Draw a pie chart on a canvas element
   */
  drawPieChart(canvas: HTMLCanvasElement, satisfied: number, unsatisfied: number, satisfiedColor: string, unsatisfiedColor: string): void {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const total = satisfied + unsatisfied;
    if (total === 0) return;

    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 1;

    // Calculate angles
    const satisfiedAngle = (satisfied / total) * 2 * Math.PI;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw satisfied portion
    if (satisfied > 0) {
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, 0, satisfiedAngle);
      ctx.closePath();
      ctx.fillStyle = satisfiedColor;
      ctx.fill();
    }

    // Draw unsatisfied portion
    if (unsatisfied > 0) {
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, satisfiedAngle, 2 * Math.PI);
      ctx.closePath();
      ctx.fillStyle = unsatisfiedColor;
      ctx.fill();
    }

    // Draw border
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 0.5;
    ctx.stroke();
  }

  /**
   * Export table data to Excel
   */
  downloadExcel(): void {
    this.table.download("xlsx", "bonus-measures.xlsx", {sheetName: "Bonus Measures"});
  }

  /**
   * Export table data to CSV
   */
  downloadCSV(): void {
    this.table.download("csv", "bonus-measures.csv");
  }

  /**
   * Export table data to PDF
   */
  downloadPDF(): void {
    this.table.download("pdf", "bonus-measures.pdf", {
      orientation: "landscape",
      title: "Bonus Measures Report"
    });
  }

  /**
   * Print the table
   */
  printTable(): void {
    this.table.print(false, true);
  }

  /**
   * Calculate the total sum of all total measures in the dataset
   */
  getTotalMeasuresSum(): number {
    if (!this.bonusMeasures || this.bonusMeasures.length === 0) {
      return 0;
    }

    return this.bonusMeasures.reduce((sum, measure) => sum + measure.totCohortCnt, 0);
  }
}
