<!-- Report Details Component -->
<div class="report-details-wrapper">
  <!-- Header -->
  <div class="header">
    <div class="header-content">
      <span class="patient-count">{{ getTotalPatients() | number }} Patients</span>
      <div class="export-dropdown">
        <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
          <button class="downloadButton" (click)="toggleMenu(); $event.stopPropagation();">
            <mat-icon class="material-symbols-outlined download-icon">ios_share</mat-icon>Download or Print
          </button>
          <nav class="menu" *ngIf="menuOpen">
            <ul>
              <li>
                <div class="menu-header">
                  <div class="menu-title">
                    <mat-icon class="material-symbols-outlined menu-icon">ios_share</mat-icon>
                    <span>Export Options</span>
                  </div>
                </div>
              </li>
              <li><a (click)="exportToExcel(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">description</mat-icon>
                Export to Excel
              </a></li>
              <li><a (click)="exportToCSV(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">table_view</mat-icon>
                Export to CSV
              </a></li>
              <li><a (click)="exportToPDF(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">picture_as_pdf</mat-icon>
                Export to PDF
              </a></li>
              <li><a (click)="printTable(); toggleMenu();">
                <mat-icon class="material-symbols-outlined">print</mat-icon>
                Print
              </a></li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabulator Table Container -->
  <div class="table-container">
    <div id="report-details-table"></div>
  </div>

  <!-- Loading Spinner -->
  <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="ball-scale-multiple">
    <p style="color: white">Loading...</p>
  </ngx-spinner>
</div>
