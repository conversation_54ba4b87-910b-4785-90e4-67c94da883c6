import { Component, OnInit, AfterViewInit, OnDestroy, Input } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { TabulatorFull as Tabulator } from 'tabulator-tables';
import { jsPDF } from 'jspdf';
import { applyPlugin } from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { ReportDetailsService } from './services/report-details.service';
import { ReportDetail } from 'src/app/shared-services/bonus-measures/model/report-details-model';

// Apply jsPDF plugin
applyPlugin(jsPDF);

@Component({
  selector: 'app-report-details',
  templateUrl: './report-details.component.html',
  styleUrl: './report-details.component.scss'
})
export class ReportDetailsComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() siteId!: string;
  @Input() year!: number;
  @Input() cohortId!: number;
  @Input() locationCd!: string;
  @Input() providerCd!: string;
  @Input() rollingWeek!: number;
  @Input() measuresCd!: string;
  @Input() alertLvl: boolean = true;

  reportDetails: ReportDetail[] = [];
  table!: Tabulator;
  private resizeObserver!: any;
  menuOpen = false;

  constructor(
    private reportDetailsService: ReportDetailsService,
    private spinnerService: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this.loadReportDetails();
  }

  ngAfterViewInit(): void {
    // Initialize resize observer for responsive table
    this.initializeResizeObserver();
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    if (this.table) {
      this.table.destroy();
    }
  }

  /**
   * Load report details data from the service
   */
  loadReportDetails(): void {
    this.spinnerService.show();

    const params = {
      siteId: this.siteId,
      year: this.year,
      cohortId: this.cohortId,
      locationCd: this.locationCd,
      providerCd: this.providerCd,
      rollingWeek: this.rollingWeek,
      alertLvl: this.alertLvl,
      measuresCd: this.measuresCd
    };

    this.reportDetailsService.getReportDetails(params).subscribe({
      next: (data) => {
        this.reportDetails = data;
        this.spinnerService.hide();

        // Initialize table after data is loaded
        setTimeout(() => this.initializeTable(), 0);
      },
      error: (error) => {
        console.error('Error loading report details data:', error);
        this.spinnerService.hide();
      }
    });
  }

  /**
   * Initialize the resize observer for responsive table behavior
   */
  initializeResizeObserver(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        if (this.table) {
          this.table.redraw();
        }
      });

      const tableContainer = document.querySelector('#report-details-table');
      if (tableContainer) {
        this.resizeObserver.observe(tableContainer);
      }
    }
  }

  /**
   * Initialize the Tabulator table with report details data
   */
  initializeTable(): void {
    this.table = new Tabulator("#report-details-table", {
      dependencies: {
        XLSX: XLSX,
        jspdf: jsPDF
      },
      downloadConfig: {
        rowGroups: false,
      },
      data: this.reportDetails,
      printAsHtml: true,
      printStyled: true,
      pagination: true,
      paginationSize: 25,
      paginationSizeSelector: [10, 25, 50, 100],
      height: "99%",
      rowHeight: 35,
      columnDefaults: {
        resizable: true,
        headerSort: true,
      },
      columns: [
        {
          title: "MRN",
          field: "mrn",
          width: 120,
          cssClass: "whiteBorder"
        },
        {
          title: "Patient Name",
          field: "patientName",
          width: 250,
          cssClass: "whiteBorder"
        },
        {
          title: "Birth Date",
          field: "formattedBirthDate",
          width: 120,
          hozAlign: "center"
        },
        {
          title: "Age",
          field: "age",
          width: 80,
          hozAlign: "center"
        },
        {
          title: "Last Visit",
          field: "formattedLastVisitDate",
          width: 120,
          hozAlign: "center"
        },
        {
          title: "Measure Date",
          field: "formattedMeasureDate",
          width: 120,
          hozAlign: "center"
        },
        {
          title: "Alert Level",
          field: "alertLvl",
          width: 100,
          hozAlign: "center",
          formatter: (cell: any) => {
            const value = cell.getValue();
            const color = value === 1 ? '#28a745' : value === 2 ? '#ffc107' : '#dc3545';
            return `<span style="color: ${color}; font-weight: bold;">${value}</span>`;
          }
        },
        {
          title: "Action",
          field: "actionTxt",
          width: 200,
          cssClass: "whiteBorder"
        },
        {
          title: "Details",
          field: "detailTxt",
          width: 300,
          formatter: "html",
          cssClass: "detail-cell"
        }
      ]
    });

    // Subscribe to table events
    this.table.on("tableBuilt", () => {
      console.log("Report details table built successfully");
    });
  }

  /**
   * Toggle the export menu
   */
  toggleMenu(): void {
    this.menuOpen = !this.menuOpen;
  }

  /**
   * Handle clicks outside the menu to close it
   */
  onClickOutsideMenu(event: Event): void {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown')) {
      this.menuOpen = false;
    }
  }

  /**
   * Export table data to Excel
   */
  exportToExcel(): void {
    this.table.download("xlsx", "report-details.xlsx", {sheetName: "Report Details"});
  }

  /**
   * Export table data to CSV
   */
  exportToCSV(): void {
    this.table.download("csv", "report-details.csv");
  }

  /**
   * Export table data to PDF
   */
  exportToPDF(): void {
    this.table.download("pdf", "report-details.pdf", {
      orientation: "landscape",
      title: "Report Details"
    });
  }

  /**
   * Print the table
   */
  printTable(): void {
    this.table.print(false, true);
  }

  /**
   * Get total number of patients
   */
  getTotalPatients(): number {
    return this.reportDetails ? this.reportDetails.length : 0;
  }
}
