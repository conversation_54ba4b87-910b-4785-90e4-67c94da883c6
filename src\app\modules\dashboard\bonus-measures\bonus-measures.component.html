<!-- Bonus Measures Dashboard -->
<div class="report-wrapper">
  <!-- Report Header -->
  <div class="report-header">
    <div class="header-left">
      <h1 class="report-title">Bonus Measures</h1>
    </div>
  </div>

  <!-- Report Content -->
  <div class="report-content-layout">
    <!-- Main Content Area -->
    <div class="report-viewer-area">
      <div class="bonus-measures-container">
        <div class="header">
          <div class="header-content">
            <span _ngcontent-ng-c1954684495="" class="qualifying-patients">2,234 Qualifying Patients: COMES FROM FILTER, Measure Year FILTER</span>
            <div class="export-dropdown">
              <div class="dropdown" (document:click)="onClickOutsideMenu($event)">
                <button class="downloadButton" (click)="toggleMenu(); $event.stopPropagation();">
                  <mat-icon class="material-symbols-outlined download-icon">ios_share</mat-icon>Download or Print
                </button>
                <nav class="menu" *ngIf="menuOpen">
                  <ul>
                    <li>
                      <div class="menu-header">
                        <div class="menu-title">
                          <mat-icon class="material-symbols-outlined menu-icon">ios_share</mat-icon>
                          <span class="export-label">Export&nbsp;Report</span>
                        </div>
                        <div class="menu-close" (click)="toggleMenu()">
                          <mat-icon class="material-symbols-outlined close-icon">close</mat-icon>
                        </div>
                      </div>
                    </li>
                    <li class="menu-separator"><button (click)="printTable();">Print</button></li>
                    <li><button (click)="downloadPDF();">PDF Export</button></li>
                    <li><button (click)="downloadExcel();">Excel Export</button></li>
                    <li><button (click)="downloadCSV();">CSV Export</button></li>
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        </div>

        <!-- Tabulator Table Container -->
        <div class="table-container">
          <div id="bonus-measures-table"></div>
        </div>

      </div>
    </div>
  </div>
</div>
