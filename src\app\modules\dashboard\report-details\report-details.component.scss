/* Report Details Component Styles */
.report-details-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: MuseoSans-300;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-count {
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
}

/* Export Dropdown Styles */
.export-dropdown {
  position: relative;
}

.downloadButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #0071BC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.downloadButton:hover {
  background-color: #005a94;
}

.download-icon {
  font-size: 18px;
}

.dropdown {
  position: relative;
}

.menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 200px;
}

.menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu li {
  border-bottom: 1px solid #eee;
}

.menu li:last-child {
  border-bottom: none;
}

.menu a {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  text-decoration: none;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu a:hover {
  background-color: #f5f5f5;
}

.menu-header {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
}

.menu-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #0071BC;
}

.menu-icon {
  font-size: 18px;
}

/* Table Container */
.table-container {
  flex: 1;
  overflow: hidden;
  padding: 16px;
}

#report-details-table {
  height: 100%;
}

/* Tabulator Overrides */
::ng-deep .tabulator {
  border: none;
  font-family: MuseoSans-300;
}

::ng-deep .tabulator-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #0071BC;
}

::ng-deep .tabulator-col-title {
  font-weight: 600;
  color: #333;
}

::ng-deep .tabulator-row {
  border-bottom: 1px solid #eee;
}

::ng-deep .tabulator-row:hover {
  background-color: #f8f9fa;
}

::ng-deep .tabulator-cell.whiteBorder {
  border-right: 1px solid #ddd;
}

::ng-deep .tabulator-cell.detail-cell {
  font-size: 12px;
  line-height: 1.4;
}

::ng-deep .tabulator-cell.detail-cell ul {
  margin: 0;
  padding-left: 16px;
}

::ng-deep .tabulator-cell.detail-cell li {
  margin-bottom: 4px;
}

/* Pagination Styles */
::ng-deep .tabulator-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #ddd;
}

::ng-deep .tabulator-page {
  background-color: #0071BC;
  color: white;
  border: 1px solid #0071BC;
}

::ng-deep .tabulator-page:hover {
  background-color: #005a94;
}

::ng-deep .tabulator-page.active {
  background-color: #005a94;
}

::ng-deep .tabulator-paginator {
  color: #333;
}

::ng-deep .tabulator-page-size {
  background-color: white;
  border: 1px solid #ddd;
  color: #333;
}
